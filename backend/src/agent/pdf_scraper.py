import os
import time
import requests
from typing import List, Dict, Optional
from urllib.parse import urljoin, urlparse
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup


class PDFScraper:
    """Selenium-based PDF scraper for extracting annual report PDFs from websites."""
    
    def __init__(self, headless: bool = True, download_dir: str = "./downloads"):
        """Initialize the PDF scraper.
        
        Args:
            headless: Whether to run browser in headless mode
            download_dir: Directory to save downloaded PDFs
        """
        self.headless = headless
        self.download_dir = os.path.abspath(download_dir)
        self.driver = None
        
        # Create download directory if it doesn't exist
        os.makedirs(self.download_dir, exist_ok=True)
    
    def _setup_driver(self) -> webdriver.Chrome:
        """Setup Chrome WebDriver with appropriate options."""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument("--headless")
        
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        # Set download preferences
        prefs = {
            "download.default_directory": self.download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True,
            "plugins.always_open_pdf_externally": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        # Setup Chrome driver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_page_load_timeout(30)
        
        return driver
    
    def find_pdf_links(self, url: str, keywords: List[str] = None) -> List[Dict[str, str]]:
        """Find PDF links on a webpage.
        
        Args:
            url: URL to scrape
            keywords: Keywords to filter PDFs (e.g., ['annual', 'report'])
            
        Returns:
            List of dictionaries with PDF information
        """
        if keywords is None:
            keywords = ['annual', 'report', 'financial', 'statement']
        
        pdf_links = []
        
        try:
            self.driver = self._setup_driver()
            self.driver.get(url)
            
            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Find all links
            links = self.driver.find_elements(By.TAG_NAME, "a")
            
            for link in links:
                try:
                    href = link.get_attribute("href")
                    text = link.text.strip().lower()
                    
                    if href and href.lower().endswith('.pdf'):
                        # Check if link text contains relevant keywords
                        if any(keyword.lower() in text for keyword in keywords):
                            pdf_info = {
                                'url': href,
                                'text': link.text.strip(),
                                'title': link.get_attribute("title") or "",
                                'source_page': url
                            }
                            pdf_links.append(pdf_info)
                
                except Exception as e:
                    continue
            
            # Also check for PDF links in the page source using BeautifulSoup
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            for a_tag in soup.find_all('a', href=True):
                href = a_tag['href']
                if href.lower().endswith('.pdf'):
                    full_url = urljoin(url, href)
                    text = a_tag.get_text(strip=True).lower()
                    
                    if any(keyword.lower() in text for keyword in keywords):
                        pdf_info = {
                            'url': full_url,
                            'text': a_tag.get_text(strip=True),
                            'title': a_tag.get('title', ''),
                            'source_page': url
                        }
                        # Avoid duplicates
                        if not any(p['url'] == full_url for p in pdf_links):
                            pdf_links.append(pdf_info)
            
        except TimeoutException:
            print(f"Timeout loading page: {url}")
        except WebDriverException as e:
            print(f"WebDriver error: {e}")
        except Exception as e:
            print(f"Error scraping {url}: {e}")
        finally:
            if self.driver:
                self.driver.quit()
        
        return pdf_links
    
    def download_pdf(self, pdf_url: str, filename: str = None) -> Optional[str]:
        """Download a PDF file.
        
        Args:
            pdf_url: URL of the PDF to download
            filename: Optional custom filename
            
        Returns:
            Path to downloaded file or None if failed
        """
        try:
            response = requests.get(pdf_url, stream=True, timeout=30)
            response.raise_for_status()
            
            if filename is None:
                # Extract filename from URL
                parsed_url = urlparse(pdf_url)
                filename = os.path.basename(parsed_url.path)
                if not filename.endswith('.pdf'):
                    filename = f"annual_report_{int(time.time())}.pdf"
            
            filepath = os.path.join(self.download_dir, filename)
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            return filepath
            
        except Exception as e:
            print(f"Error downloading PDF {pdf_url}: {e}")
            return None
    
    def scrape_and_download_pdfs(self, url: str, keywords: List[str] = None) -> List[Dict[str, str]]:
        """Find and download all relevant PDFs from a webpage.
        
        Args:
            url: URL to scrape
            keywords: Keywords to filter PDFs
            
        Returns:
            List of dictionaries with PDF information and download paths
        """
        pdf_links = self.find_pdf_links(url, keywords)
        results = []
        
        for pdf_info in pdf_links:
            pdf_url = pdf_info['url']
            filename = f"{pdf_info['text'].replace(' ', '_')[:50]}.pdf"
            
            download_path = self.download_pdf(pdf_url, filename)
            
            result = pdf_info.copy()
            result['download_path'] = download_path
            result['downloaded'] = download_path is not None
            
            results.append(result)
        
        return results


def scrape_annual_reports(website_url: str, company_name: str) -> List[Dict[str, str]]:
    """Convenience function to scrape annual reports from a website.
    
    Args:
        website_url: URL of the investor relations or annual reports page
        company_name: Name of the company (for organizing downloads)
        
    Returns:
        List of PDF information dictionaries
    """
    # Create company-specific download directory
    download_dir = f"./downloads/{company_name.replace(' ', '_')}"
    
    scraper = PDFScraper(headless=True, download_dir=download_dir)
    
    # Keywords specifically for annual reports
    keywords = ['annual report', 'annual', 'report', '10-K', '10K', 'form 10-K']
    
    return scraper.scrape_and_download_pdfs(website_url, keywords)
