import os
import re
from typing import Dict, Any, List
from dotenv import load_dotenv
from langchain_core.messages import AIMessage
from langgraph.graph import StateGraph, START, END
from langchain_core.runnables import RunnableConfig
from google.genai import Client
from langchain_google_genai import ChatGoogleGenerativeAI

from agent.state import PowerPlantState, PowerPlantSearchResult
from agent.configuration import Configuration
from agent.power_plant_prompts import (
    get_current_date,
    power_plant_direct_search_prompt,
    holding_company_search_prompt,
    holding_company_annual_reports_prompt,
    website_analysis_prompt,
    final_result_prompt
)
from agent.pdf_scraper import scrape_annual_reports
from agent.utils import get_citations, insert_citation_markers, resolve_urls

load_dotenv()

if os.getenv("GEMINI_API_KEY") is None:
    raise ValueError("GEMINI_API_KEY is not set")

# Used for Google Search API
genai_client = Client(api_key=os.getenv("GEMINI_API_KEY"))


def search_power_plant_direct(state: PowerPlantState, config: RunnableConfig) -> Dict[str, Any]:
    """Search directly for the power plant's annual reports."""
    configurable = Configuration.from_runnable_config(config)
    
    # Format the prompt for direct power plant search
    current_date = get_current_date()
    formatted_prompt = power_plant_direct_search_prompt.format(
        power_plant_name=state["power_plant_name"],
        current_date=current_date
    )
    
    # Use Google Search API
    response = genai_client.models.generate_content(
        model=configurable.query_generator_model,
        contents=formatted_prompt,
        config={
            "tools": [{"google_search": {}}],
            "temperature": 0,
        },
    )
    
    # Process search results
    resolved_urls = resolve_urls(
        response.candidates[0].grounding_metadata.grounding_chunks, 0
    )
    citations = get_citations(response, resolved_urls)
    modified_text = insert_citation_markers(response.text, citations)
    
    # Extract potential investor relations URLs
    investor_urls = extract_investor_relations_urls(response.text, citations)
    
    return {
        "direct_search_results": [modified_text],
        "investor_relations_urls": investor_urls,
        "search_strategy": "direct"
    }


def search_holding_company(state: PowerPlantState, config: RunnableConfig) -> Dict[str, Any]:
    """Search for the holding company of the power plant."""
    configurable = Configuration.from_runnable_config(config)
    
    # Format the prompt for holding company search
    current_date = get_current_date()
    formatted_prompt = holding_company_search_prompt.format(
        power_plant_name=state["power_plant_name"],
        current_date=current_date
    )
    
    # Use Google Search API
    response = genai_client.models.generate_content(
        model=configurable.query_generator_model,
        contents=formatted_prompt,
        config={
            "tools": [{"google_search": {}}],
            "temperature": 0,
        },
    )
    
    # Extract holding company name from response
    holding_company_name = extract_holding_company_name(response.text)
    
    return {
        "holding_company_name": holding_company_name,
        "search_strategy": "holding_company"
    }


def search_holding_company_reports(state: PowerPlantState, config: RunnableConfig) -> Dict[str, Any]:
    """Search for annual reports of the holding company."""
    configurable = Configuration.from_runnable_config(config)
    
    if not state.get("holding_company_name"):
        return {"status": "not_found"}
    
    # Format the prompt for holding company annual reports search
    current_date = get_current_date()
    formatted_prompt = holding_company_annual_reports_prompt.format(
        holding_company_name=state["holding_company_name"],
        power_plant_name=state["power_plant_name"],
        current_date=current_date
    )
    
    # Use Google Search API
    response = genai_client.models.generate_content(
        model=configurable.query_generator_model,
        contents=formatted_prompt,
        config={
            "tools": [{"google_search": {}}],
            "temperature": 0,
        },
    )
    
    # Process search results
    resolved_urls = resolve_urls(
        response.candidates[0].grounding_metadata.grounding_chunks, 1
    )
    citations = get_citations(response, resolved_urls)
    modified_text = insert_citation_markers(response.text, citations)
    
    # Extract potential investor relations URLs
    investor_urls = extract_investor_relations_urls(response.text, citations)
    
    return {
        "holding_company_search_results": [modified_text],
        "investor_relations_urls": investor_urls
    }


def analyze_websites(state: PowerPlantState, config: RunnableConfig) -> Dict[str, Any]:
    """Analyze found websites to identify the best source for annual reports."""
    if not state.get("investor_relations_urls"):
        return {"status": "not_found"}
    
    best_url = None
    best_score = 0
    
    # Analyze each potential URL
    for url_info in state["investor_relations_urls"]:
        url = url_info.get("url", "")
        
        # Score URLs based on keywords
        score = score_url_for_annual_reports(url, url_info.get("text", ""))
        
        if score > best_score:
            best_score = score
            best_url = url
    
    return {
        "final_website_url": best_url,
        "status": "found" if best_url else "not_found"
    }


def collect_pdfs(state: PowerPlantState, config: RunnableConfig) -> Dict[str, Any]:
    """Collect PDF annual reports from the identified website."""
    if not state.get("final_website_url") or state.get("status") != "found":
        return {"annual_report_pdfs": [], "status": "not_found"}
    
    try:
        # Determine company name for organizing downloads
        company_name = state.get("holding_company_name") or state["power_plant_name"]
        
        # Scrape PDFs from the website
        pdf_results = scrape_annual_reports(state["final_website_url"], company_name)
        
        return {
            "annual_report_pdfs": pdf_results,
            "status": "found" if pdf_results else "not_found"
        }
    
    except Exception as e:
        print(f"Error collecting PDFs: {e}")
        return {"annual_report_pdfs": [], "status": "not_found"}


def finalize_power_plant_search(state: PowerPlantState, config: RunnableConfig) -> Dict[str, Any]:
    """Finalize the power plant search and return results."""
    if state.get("status") == "found" and state.get("final_website_url"):
        company_name = state.get("holding_company_name") or state["power_plant_name"]
        
        result = PowerPlantSearchResult(
            website_url=state["final_website_url"],
            pdf_urls=[pdf["url"] for pdf in state.get("annual_report_pdfs", [])],
            source_type=state.get("search_strategy", "unknown"),
            company_name=company_name
        )
        
        # Create final message
        message_content = f"""
Power Plant Annual Report Search Results:

Power Plant: {state['power_plant_name']}
Company: {company_name}
Search Strategy: {state.get('search_strategy', 'unknown')}
Website URL: {state['final_website_url']}

Found {len(result['pdf_urls'])} annual report PDFs:
{chr(10).join([f"- {pdf.get('text', 'Annual Report')}: {pdf['url']}" for pdf in state.get('annual_report_pdfs', [])])}

PDFs have been downloaded to: ./downloads/{company_name.replace(' ', '_')}/
"""
    else:
        message_content = f"""
Power Plant Annual Report Search Results:

Power Plant: {state['power_plant_name']}
Status: No annual reports found

The search was unable to locate annual reports for this power plant or its holding company.
"""
    
    return {
        "messages": [AIMessage(content=message_content)]
    }


# Helper functions
def extract_investor_relations_urls(text: str, citations: List[Dict]) -> List[Dict[str, str]]:
    """Extract potential investor relations URLs from search results."""
    urls = []
    
    for citation in citations:
        for segment in citation.get("segments", []):
            url = segment.get("value", "")
            label = segment.get("label", "")
            
            # Check if URL or label suggests investor relations
            if any(keyword in url.lower() or keyword in label.lower() 
                   for keyword in ["investor", "financial", "annual", "report", "ir"]):
                urls.append({
                    "url": url,
                    "text": label,
                    "source": "search_result"
                })
    
    return urls


def extract_holding_company_name(text: str) -> str:
    """Extract holding company name from search results."""
    # Simple extraction - look for company names in the text
    # This could be enhanced with more sophisticated NLP
    lines = text.split('\n')
    for line in lines:
        if any(keyword in line.lower() for keyword in ["holding", "parent", "owns", "owned by"]):
            # Extract potential company name
            words = line.split()
            for i, word in enumerate(words):
                if word.lower() in ["holding", "company", "corporation", "corp", "inc"]:
                    # Look for company name before this word
                    if i > 0:
                        potential_name = " ".join(words[max(0, i-3):i+1])
                        return potential_name.strip()
    
    return ""


def score_url_for_annual_reports(url: str, text: str) -> int:
    """Score a URL based on how likely it is to contain annual reports."""
    score = 0
    
    # URL-based scoring
    url_lower = url.lower()
    if "investor" in url_lower:
        score += 10
    if "annual" in url_lower:
        score += 8
    if "financial" in url_lower:
        score += 6
    if "report" in url_lower:
        score += 5
    if "/ir/" in url_lower or "/investor" in url_lower:
        score += 7
    
    # Text-based scoring
    text_lower = text.lower()
    if "annual report" in text_lower:
        score += 10
    if "investor relations" in text_lower:
        score += 8
    if "financial statements" in text_lower:
        score += 6
    
    return score


# Routing functions
def should_search_holding_company(state: PowerPlantState) -> str:
    """Determine if we should search for holding company."""
    # If direct search found good results, proceed to analysis
    if state.get("investor_relations_urls") and len(state["investor_relations_urls"]) > 0:
        return "analyze_websites"
    else:
        return "search_holding_company"


def should_analyze_or_search_holding_reports(state: PowerPlantState) -> str:
    """Determine next step after holding company search."""
    if state.get("holding_company_name"):
        return "search_holding_company_reports"
    else:
        return "finalize_power_plant_search"


def should_collect_pdfs(state: PowerPlantState) -> str:
    """Determine if we should collect PDFs."""
    if state.get("final_website_url") and state.get("status") == "found":
        return "collect_pdfs"
    else:
        return "finalize_power_plant_search"


# Create the Power Plant Search Graph
builder = StateGraph(PowerPlantState, config_schema=Configuration)

# Add nodes
builder.add_node("search_power_plant_direct", search_power_plant_direct)
builder.add_node("search_holding_company", search_holding_company)
builder.add_node("search_holding_company_reports", search_holding_company_reports)
builder.add_node("analyze_websites", analyze_websites)
builder.add_node("collect_pdfs", collect_pdfs)
builder.add_node("finalize_power_plant_search", finalize_power_plant_search)

# Set entry point
builder.add_edge(START, "search_power_plant_direct")

# Add conditional edges
builder.add_conditional_edges(
    "search_power_plant_direct",
    should_search_holding_company,
    ["analyze_websites", "search_holding_company"]
)

builder.add_conditional_edges(
    "search_holding_company",
    should_analyze_or_search_holding_reports,
    ["search_holding_company_reports", "finalize_power_plant_search"]
)

builder.add_edge("search_holding_company_reports", "analyze_websites")

builder.add_conditional_edges(
    "analyze_websites",
    should_collect_pdfs,
    ["collect_pdfs", "finalize_power_plant_search"]
)

builder.add_edge("collect_pdfs", "finalize_power_plant_search")
builder.add_edge("finalize_power_plant_search", END)

# Compile the graph
power_plant_graph = builder.compile(name="power-plant-search-agent")
