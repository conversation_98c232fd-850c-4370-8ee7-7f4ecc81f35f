from datetime import datetime


def get_current_date():
    return datetime.now().strftime("%B %d, %Y")


# Power Plant Direct Search Prompt
power_plant_direct_search_prompt = """You are searching for annual reports for the power plant: "{power_plant_name}".

Your goal is to find the official website or investor relations page where annual reports are available for download.

Search Strategy:
1. Search for "{power_plant_name} annual reports"
2. Look for official company websites
3. Focus on finding investor relations, financial statements, or financials sections
4. Identify pages that contain downloadable annual report PDFs

Instructions:
- Look for official company websites (avoid third-party sites)
- Prioritize investor relations or financial sections
- Note any direct links to annual report PDFs
- If you find a dedicated annual reports page, that's ideal
- Current date is {current_date}

Power Plant Name: {power_plant_name}
"""

# Holding Company Search Prompt
holding_company_search_prompt = """You are searching for the holding company of the power plant: "{power_plant_name}".

Your goal is to identify the parent/holding company that owns this power plant.

Search Strategy:
1. Search for "{power_plant_name} power plant holding company"
2. Search for "{power_plant_name} parent company"
3. Look for ownership information
4. Identify the main corporate entity that owns the power plant

Instructions:
- Focus on finding the parent company or holding company name
- Look for corporate ownership structures
- Avoid subsidiaries, focus on the main holding company
- Current date is {current_date}

Power Plant Name: {power_plant_name}
"""

# Holding Company Annual Reports Search Prompt
holding_company_annual_reports_prompt = """You are searching for annual reports for the holding company: "{holding_company_name}".

This holding company owns the power plant: "{power_plant_name}".

Your goal is to find the official website or investor relations page where annual reports are available for download.

Search Strategy:
1. Search for "{holding_company_name} annual reports"
2. Look for the official company website
3. Focus on finding investor relations, financial statements, or financials sections
4. Identify pages that contain downloadable annual report PDFs

Instructions:
- Look for official company websites (avoid third-party sites)
- Prioritize investor relations or financial sections
- Note any direct links to annual report PDFs
- If you find a dedicated annual reports page, that's ideal
- Current date is {current_date}

Holding Company Name: {holding_company_name}
Power Plant Name: {power_plant_name}
"""

# Website Analysis Prompt
website_analysis_prompt = """You are analyzing a website to find annual report PDFs.

Website URL: {website_url}
Company: {company_name}

Your goal is to:
1. Identify if this website contains annual reports
2. Look for investor relations, financial statements, or financials sections
3. Find direct links to annual report PDFs
4. Determine if this is the correct page for PDF downloads

Instructions:
- Focus on finding downloadable PDF annual reports
- Look for sections like "Investor Relations", "Financial Statements", "Financials", "Annual Reports"
- If annual reports and financial statements are separate, prioritize annual reports
- Provide the specific URL where PDFs can be downloaded
- Current date is {current_date}

Analyze this website and determine if it contains annual report PDFs.
"""

# Final Result Formatting Prompt
final_result_prompt = """Based on the search results, provide the final website URL where annual report PDFs can be found.

Search Results Summary:
- Power Plant: {power_plant_name}
- Search Strategy Used: {search_strategy}
- Company Found: {company_name}

Instructions:
- Provide only the specific webpage URL where annual report PDFs are available
- This should be a direct link to the investor relations or annual reports section
- Do not provide the main company homepage unless that's where the PDFs are located
- If no annual reports were found, clearly state "No annual reports found"

Current date is {current_date}
"""
