#!/usr/bin/env python3
"""
Power Plant Annual Report Search CLI

This CLI tool searches for annual reports of power plants using a two-step approach:
1. Direct search for the power plant's annual reports
2. If not found, search for the holding company and then its annual reports

Usage:
    python power_plant_cli.py "Power Plant Name"
    python power_plant_cli.py "Power Plant Name" --download-pdfs
    python power_plant_cli.py "Power Plant Name" --max-loops 3
"""

import argparse
import sys
import os
from typing import Dict, Any

# Add the src directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from agent.power_plant_graph import power_plant_graph
from agent.state import PowerPlantState


def print_banner():
    """Print the application banner."""
    print("=" * 60)
    print("🏭 Power Plant Annual Report Search Engine")
    print("=" * 60)
    print()


def print_search_progress(step: str, details: str = ""):
    """Print search progress updates."""
    print(f"🔍 {step}")
    if details:
        print(f"   {details}")
    print()


def format_results(result: Dict[str, Any]) -> str:
    """Format the search results for display."""
    messages = result.get("messages", [])
    if not messages:
        return "No results found."
    
    return messages[-1].content


def main():
    """Main CLI function."""
    parser = argparse.ArgumentParser(
        description="Search for annual reports of power plants",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python power_plant_cli.py "Palo Verde Nuclear Generating Station"
  python power_plant_cli.py "Three Mile Island" --download-pdfs
  python power_plant_cli.py "Diablo Canyon" --max-loops 3 --verbose
        """
    )
    
    parser.add_argument(
        "power_plant_name",
        help="Name of the power plant to search for"
    )
    
    parser.add_argument(
        "--download-pdfs",
        action="store_true",
        help="Download found PDF files (default: False)"
    )
    
    parser.add_argument(
        "--max-loops",
        type=int,
        default=2,
        help="Maximum number of search loops (default: 2)"
    )
    
    parser.add_argument(
        "--output-dir",
        default="./downloads",
        help="Directory to save downloaded PDFs (default: ./downloads)"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose output"
    )
    
    parser.add_argument(
        "--headless",
        action="store_true",
        default=True,
        help="Run browser in headless mode (default: True)"
    )
    
    args = parser.parse_args()
    
    # Print banner
    print_banner()
    
    # Validate power plant name
    if not args.power_plant_name.strip():
        print("❌ Error: Power plant name cannot be empty")
        sys.exit(1)
    
    # Check for required environment variables
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY environment variable is not set")
        print("Please set your Google Gemini API key:")
        print("export GEMINI_API_KEY='your-api-key-here'")
        sys.exit(1)
    
    print(f"🏭 Searching for: {args.power_plant_name}")
    print(f"📁 Download directory: {args.output_dir}")
    print(f"📥 Download PDFs: {'Yes' if args.download_pdfs else 'No'}")
    print()
    
    # Initialize the state
    initial_state: PowerPlantState = {
        "power_plant_name": args.power_plant_name.strip(),
        "holding_company_name": "",
        "direct_search_results": [],
        "holding_company_search_results": [],
        "investor_relations_urls": [],
        "annual_report_pdfs": [],
        "final_website_url": "",
        "search_strategy": "",
        "status": "searching"
    }
    
    try:
        print_search_progress("Starting power plant annual report search...")
        
        # Configure the graph
        config = {
            "configurable": {
                "max_research_loops": args.max_loops,
                "download_pdfs": args.download_pdfs,
                "output_dir": args.output_dir,
                "verbose": args.verbose,
                "headless": args.headless
            }
        }
        
        # Run the search
        if args.verbose:
            print_search_progress("Executing search graph...")
        
        result = power_plant_graph.invoke(initial_state, config=config)
        
        # Display results
        print("=" * 60)
        print("📊 SEARCH RESULTS")
        print("=" * 60)
        print()
        
        formatted_results = format_results(result)
        print(formatted_results)
        
        # Additional information if verbose
        if args.verbose:
            print("\n" + "=" * 60)
            print("🔧 DETAILED INFORMATION")
            print("=" * 60)
            print()
            
            print(f"Search Strategy: {result.get('search_strategy', 'Unknown')}")
            print(f"Holding Company: {result.get('holding_company_name', 'Not found')}")
            print(f"Final Status: {result.get('status', 'Unknown')}")
            print(f"Website URL: {result.get('final_website_url', 'Not found')}")
            
            if result.get('annual_report_pdfs'):
                print(f"\nFound {len(result['annual_report_pdfs'])} PDF(s):")
                for i, pdf in enumerate(result['annual_report_pdfs'], 1):
                    print(f"  {i}. {pdf.get('text', 'Unknown')}")
                    print(f"     URL: {pdf.get('url', 'Unknown')}")
                    if pdf.get('download_path'):
                        print(f"     Downloaded to: {pdf['download_path']}")
        
        # Summary
        print("\n" + "=" * 60)
        print("✅ Search completed successfully!")
        
        if result.get('final_website_url'):
            print(f"🌐 Website: {result['final_website_url']}")
        
        if result.get('annual_report_pdfs'):
            pdf_count = len(result['annual_report_pdfs'])
            print(f"📄 Found {pdf_count} annual report PDF{'s' if pdf_count != 1 else ''}")
            
            if args.download_pdfs:
                downloaded_count = sum(1 for pdf in result['annual_report_pdfs'] if pdf.get('downloaded'))
                print(f"📥 Downloaded {downloaded_count} PDF{'s' if downloaded_count != 1 else ''}")
        
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n❌ Search interrupted by user")
        sys.exit(1)
    
    except Exception as e:
        print(f"\n❌ Error during search: {str(e)}")
        if args.verbose:
            import traceback
            print("\nFull error traceback:")
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
