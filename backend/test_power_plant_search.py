#!/usr/bin/env python3
"""
Test script for the Power Plant Annual Report Search Engine
"""

import os
import sys
from dotenv import load_dotenv

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.power_plant_graph import power_plant_graph
from agent.state import PowerPlantState

# Load environment variables
load_dotenv()

def test_power_plant_search():
    """Test the power plant search functionality."""
    
    # Check for API key
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY environment variable is not set")
        print("Please set your Google Gemini API key:")
        print("export GEMINI_API_KEY='your-api-key-here'")
        return False
    
    print("🏭 Testing Power Plant Annual Report Search Engine")
    print("=" * 60)
    
    # Test with a well-known power plant
    test_power_plant = "Palo Verde Nuclear Generating Station"
    
    print(f"🔍 Testing with: {test_power_plant}")
    print()
    
    # Initialize the state
    initial_state: PowerPlantState = {
        "power_plant_name": test_power_plant,
        "holding_company_name": "",
        "direct_search_results": [],
        "holding_company_search_results": [],
        "investor_relations_urls": [],
        "annual_report_pdfs": [],
        "final_website_url": "",
        "search_strategy": "",
        "status": "searching"
    }
    
    try:
        print("🚀 Starting search...")
        
        # Run the search (without PDF downloading for testing)
        config = {
            "configurable": {
                "max_research_loops": 2,
                "download_pdfs": False,
                "verbose": True
            }
        }
        
        result = power_plant_graph.invoke(initial_state, config=config)
        
        print("\n" + "=" * 60)
        print("📊 TEST RESULTS")
        print("=" * 60)
        
        # Check if we got results
        if result.get("messages"):
            print("✅ Search completed successfully!")
            print("\nResult:")
            print(result["messages"][-1].content)
            
            # Print additional details
            print(f"\nSearch Strategy: {result.get('search_strategy', 'Unknown')}")
            print(f"Holding Company: {result.get('holding_company_name', 'Not found')}")
            print(f"Final Status: {result.get('status', 'Unknown')}")
            print(f"Website URL: {result.get('final_website_url', 'Not found')}")
            
            if result.get('investor_relations_urls'):
                print(f"Found {len(result['investor_relations_urls'])} potential URLs")
            
            return True
        else:
            print("❌ No results returned from search")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_power_plant_search()
    sys.exit(0 if success else 1)
