# Power Plant Annual Report Search Engine

A specialized search engine built with LangGraph for finding annual reports of power plants. This tool uses a two-step approach to locate annual reports:

1. **Direct Search**: Searches directly for the power plant's annual reports
2. **Holding Company Search**: If direct search fails, finds the holding company and searches for its annual reports

## Features

- 🏭 **Power Plant Focused**: Specialized for power plant annual report searches
- 🔍 **Two-Step Search Strategy**: Direct search + holding company fallback
- 📄 **PDF Collection**: Automatically finds and downloads annual report PDFs
- 🌐 **Website Analysis**: Identifies investor relations pages and financial sections
- 🤖 **AI-Powered**: Uses Google Gemini models for intelligent search
- 💻 **Terminal Interface**: No frontend required, pure CLI operation

## Prerequisites

1. **Python 3.11+**
2. **Google Gemini API Key**
3. **Chrome Browser** (for Selenium web scraping)

## Installation

1. Install dependencies:
```bash
cd backend
pip install -e .
```

2. Set up your Google Gemini API key:
```bash
export GEMINI_API_KEY='your-api-key-here'
```

## Usage

### Basic Search
```bash
cd backend
python src/agent/power_plant_cli.py "Power Plant Name"
```

### Search with PDF Download
```bash
python src/agent/power_plant_cli.py "Power Plant Name" --download-pdfs
```

### Advanced Options
```bash
python src/agent/power_plant_cli.py "Power Plant Name" \
    --download-pdfs \
    --max-loops 3 \
    --output-dir ./my_downloads \
    --verbose
```

### Examples
```bash
# Search for Palo Verde Nuclear Generating Station
python src/agent/power_plant_cli.py "Palo Verde Nuclear Generating Station"

# Search and download PDFs for Three Mile Island
python src/agent/power_plant_cli.py "Three Mile Island" --download-pdfs

# Verbose search for Diablo Canyon
python src/agent/power_plant_cli.py "Diablo Canyon" --verbose
```

## How It Works

### Search Strategy

1. **Direct Power Plant Search**
   - Searches for "{power plant name} annual reports"
   - Looks for official company websites
   - Identifies investor relations sections

2. **Holding Company Search** (if direct search fails)
   - Searches for "{power plant name} power plant holding company"
   - Identifies the parent company
   - Searches for "{holding company name} annual reports"

3. **Website Analysis**
   - Analyzes found websites for annual report sections
   - Prioritizes investor relations, financial statements, and financials sections
   - Scores URLs based on relevance

4. **PDF Collection**
   - Uses Selenium to scrape PDF links from identified pages
   - Downloads annual report PDFs to organized directories
   - Filters for annual reports (not just any financial documents)

### Output

The tool provides:
- **Website URL**: Direct link to the page containing annual reports
- **PDF List**: All found annual report PDFs with download links
- **Company Information**: Whether reports are from the power plant directly or its holding company
- **Download Status**: Information about successfully downloaded files

## Configuration Options

| Option | Description | Default |
|--------|-------------|---------|
| `--download-pdfs` | Download found PDF files | False |
| `--max-loops` | Maximum search iterations | 2 |
| `--output-dir` | PDF download directory | ./downloads |
| `--verbose` | Enable detailed output | False |
| `--headless` | Run browser in headless mode | True |

## File Structure

```
backend/
├── src/agent/
│   ├── power_plant_cli.py          # Main CLI interface
│   ├── power_plant_graph.py        # LangGraph workflow
│   ├── power_plant_prompts.py      # Specialized prompts
│   ├── pdf_scraper.py              # Selenium PDF scraper
│   └── state.py                    # State definitions
├── downloads/                      # Downloaded PDFs (created automatically)
└── test_power_plant_search.py      # Test script
```

## Testing

Run the test script to verify functionality:
```bash
cd backend
python test_power_plant_search.py
```

## Troubleshooting

### Common Issues

1. **Missing API Key**
   ```
   Error: GEMINI_API_KEY environment variable is not set
   ```
   Solution: Set your Google Gemini API key as an environment variable

2. **Chrome Driver Issues**
   ```
   WebDriverException: chrome not reachable
   ```
   Solution: Ensure Chrome browser is installed. The webdriver-manager will handle ChromeDriver automatically.

3. **No Results Found**
   - Try different variations of the power plant name
   - Some power plants may not have publicly available annual reports
   - Check if the power plant is owned by a publicly traded company

### Debug Mode

Use `--verbose` flag for detailed output:
```bash
python src/agent/power_plant_cli.py "Power Plant Name" --verbose
```

## API Integration

The search engine runs on LangGraph server. To use the API:

1. Start the LangGraph server:
```bash
cd backend
langgraph up
```

2. The API will be available at `http://localhost:8123`

## Limitations

- Only searches for publicly available annual reports
- Requires power plants to be owned by companies that publish annual reports
- Some private or municipal power plants may not have searchable annual reports
- PDF extraction depends on website structure and may not work for all sites

## Contributing

To extend the search engine:

1. **Add new search strategies** in `power_plant_graph.py`
2. **Improve prompts** in `power_plant_prompts.py`
3. **Enhance PDF extraction** in `pdf_scraper.py`
4. **Add new state fields** in `state.py`
